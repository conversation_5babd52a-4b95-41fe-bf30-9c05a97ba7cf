import React, { useEffect, useState, useRef } from "react";
import { supabase } from "../../lib/supabaseClient";
import { Tables } from "../../types/supabase";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { X, FileText, Download, ZoomIn, ZoomOut, Trash2 } from "lucide-react";
import Spinner from "@/components/ui/Spinner";
import * as pdfjsLib from 'pdfjs-dist';
import MarkdownRenderer from "./MarkdownRenderer";

type StudyDocument = Tables<"study_documents">;

interface InlineDocumentViewerProps {
  document: StudyDocument;
  onClose: () => void;
  onDelete?: (document: StudyDocument) => void;
}

const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://localhost:5000/api";

// Set up PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.4.168/pdf.worker.min.mjs';

// Helper to get auth token
async function getAuthToken(): Promise<string | null> {
  try {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    return session?.access_token || null;
  } catch (error) {
    console.error("Error getting auth token:", error);
    return null;
  }
}

export const InlineDocumentViewer: React.FC<InlineDocumentViewerProps> = ({
  document,
  onClose,
  onDelete,
}) => {
  const [content, setContent] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pdfDoc, setPdfDoc] = useState<pdfjsLib.PDFDocumentProxy | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [scale, setScale] = useState(1.0);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isPdf, setIsPdf] = useState(false);

  const isPdfFile = (contentType: string | null, fileName: string) => {
    return contentType === 'application/pdf' || fileName.toLowerCase().endsWith('.pdf');
  };

  useEffect(() => {
    const fetchDocumentContent = async () => {
      if (!document?.id) {
        setContent(null);
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);
      setContent(null);
      setPdfDoc(null);
      setCurrentPage(1);
      setTotalPages(0);

      const isDocumentPdf = isPdfFile(document.content_type, document.file_name);
      setIsPdf(isDocumentPdf);

      try {
        const token = await getAuthToken();
        if (!token) {
          throw new Error("Authentication required");
        }

        if (isDocumentPdf) {
          // Try to fetch PDF binary data first
          try {
            const response = await fetch(
              `${API_BASE_URL}/documents/${document.id}/file`,
              {
                method: "GET",
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              }
            );

            if (response.ok) {
              const arrayBuffer = await response.arrayBuffer();
              const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
              const pdf = await loadingTask.promise;

              setPdfDoc(pdf);
              setTotalPages(pdf.numPages);
            } else {
              // If original PDF is not available, fall back to extracted text
              console.log("Original PDF not available, falling back to extracted text");
              setIsPdf(false);
              throw new Error("Original PDF not available");
            }
          } catch (pdfError) {
            // Fall back to extracted text for PDFs when original file is not available
            console.log("PDF loading failed, falling back to extracted text:", pdfError);
            setIsPdf(false);
            // Continue to text content fetch below
          }
        }

        if (!isPdf || !pdfDoc) {
          // Fetch extracted text content for non-PDF files or when PDF original is not available
          const response = await fetch(
            `${API_BASE_URL}/documents/${document.id}/content`,
            {
              method: "GET",
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(
              errorData.error || `HTTP ${response.status}: ${response.statusText}`
            );
          }

          const textContent = await response.text();
          setContent(textContent);
        }
      } catch (err: any) {
        console.error("Error fetching document content:", err);
        setError(err.message || "Failed to load document content.");
      } finally {
        setLoading(false);
      }
    };

    fetchDocumentContent();
  }, [document]);

  // Render PDF page
  useEffect(() => {
    const renderPage = async () => {
      if (!pdfDoc || !canvasRef.current) return;

      try {
        const page = await pdfDoc.getPage(currentPage);
        const viewport = page.getViewport({ scale });
        
        const canvas = canvasRef.current;
        const context = canvas.getContext('2d');
        if (!context) return;

        canvas.height = viewport.height;
        canvas.width = viewport.width;

        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        };

        await page.render(renderContext).promise;
      } catch (err) {
        console.error("Error rendering PDF page:", err);
      }
    };

    if (pdfDoc && currentPage) {
      renderPage();
    }
  }, [pdfDoc, currentPage, scale]);

  const handleDownload = async () => {
    if (!content || !document) return;

    try {
      const blob = new Blob([content], { type: "text/plain" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `${document.file_name}_extracted.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error("Error downloading content:", err);
    }
  };

  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const zoomIn = () => {
    setScale(Math.min(scale * 1.2, 3.0));
  };

  const zoomOut = () => {
    setScale(Math.max(scale / 1.2, 0.5));
  };

  const handleDelete = () => {
    if (onDelete && document) {
      const confirmDelete = window.confirm(
        `Are you sure you want to delete "${document.file_name}"? This action cannot be undone.`
      );
      if (confirmDelete) {
        onDelete(document);
      }
    }
  };

  return (
    <Card className="h-[60vh] bg-slate-800 border-slate-700 flex flex-col">
      <CardHeader className="flex flex-row items-center justify-between py-3 px-4 border-b border-slate-700">
        <div className="flex items-center space-x-2 min-w-0 flex-1">
          <FileText className="h-4 w-4 text-purple-400 flex-shrink-0" />
          <CardTitle className="text-sm text-purple-400 truncate">
            {document?.file_name || "Document Viewer"}
          </CardTitle>
        </div>
        <div className="flex items-center space-x-1 flex-shrink-0">
          {isPdf && pdfDoc && (
            <>
              <Button
                onClick={zoomOut}
                variant="ghost"
                size="sm"
                className="h-7 w-7 p-0 text-slate-300 hover:bg-slate-700"
              >
                <ZoomOut className="h-3 w-3" />
              </Button>
              <Button
                onClick={zoomIn}
                variant="ghost"
                size="sm"
                className="h-7 w-7 p-0 text-slate-300 hover:bg-slate-700"
              >
                <ZoomIn className="h-3 w-3" />
              </Button>
              <span className="text-xs text-slate-400 px-2">
                {currentPage}/{totalPages}
              </span>
              <Button
                onClick={prevPage}
                disabled={currentPage <= 1}
                variant="ghost"
                size="sm"
                className="h-7 px-2 text-xs text-slate-300 hover:bg-slate-700 disabled:opacity-50"
              >
                Prev
              </Button>
              <Button
                onClick={nextPage}
                disabled={currentPage >= totalPages}
                variant="ghost"
                size="sm"
                className="h-7 px-2 text-xs text-slate-300 hover:bg-slate-700 disabled:opacity-50"
              >
                Next
              </Button>
            </>
          )}
          {content && !isPdf && (
            <Button
              onClick={handleDownload}
              variant="ghost"
              size="sm"
              className="h-7 w-7 p-0 text-slate-300 hover:bg-slate-700"
            >
              <Download className="h-3 w-3" />
            </Button>
          )}
          {onDelete && (
            <Button
              onClick={handleDelete}
              variant="ghost"
              size="sm"
              className="h-7 w-7 p-0 text-red-400 hover:bg-red-500/20 hover:text-red-300"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          )}
          <Button
            onClick={onClose}
            variant="ghost"
            size="sm"
            className="h-7 w-7 p-0 text-slate-300 hover:bg-slate-700"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="flex-1 p-0 overflow-hidden">
        {loading && (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <Spinner size="lg" />
              <p className="text-slate-300 mt-4 text-sm">
                Loading document...
              </p>
            </div>
          </div>
        )}

        {error && (
          <div className="flex items-center justify-center h-full p-4">
            <div className="text-center">
              <p className="text-red-400 mb-4 text-sm">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
                size="sm"
                className="text-slate-300 border-slate-600 hover:bg-slate-700"
              >
                Retry
              </Button>
            </div>
          </div>
        )}

        {isPdf && pdfDoc && !loading && !error && (
          <div className="h-full overflow-auto bg-slate-900">
            <div className="flex justify-center p-4">
              <canvas
                ref={canvasRef}
                className="border border-slate-600 shadow-lg"
                style={{ maxWidth: '100%', height: 'auto' }}
              />
            </div>
          </div>
        )}

        {!isPdf && content && !loading && !error && (
          <ScrollArea className="h-full">
            <div className="p-4">
              <div className="bg-slate-900 p-4 rounded-lg">
                <div className="mb-4 text-xs text-slate-400 border-b border-slate-700 pb-2">
                  <p><strong>File:</strong> {document.file_name}</p>
                  <p><strong>Type:</strong> {document.content_type}</p>
                  <p><strong>Size:</strong> {document.size_bytes ? `${(document.size_bytes / 1024).toFixed(1)} KB` : "Unknown"}</p>
                </div>
                <div>
                  <h3 className="text-slate-300 font-medium mb-3 text-sm">
                    Extracted Content:
                  </h3>
                  <MarkdownRenderer
                    content={content}
                    className="text-xs leading-relaxed"
                  />
                </div>
              </div>
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
};
