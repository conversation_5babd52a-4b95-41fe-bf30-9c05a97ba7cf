import React, { useEffect, useState } from "react";
import { supabase } from "../../lib/supabaseClient";
import { useAuth } from "../../hooks/useAuth";
import { Tables } from "../../types/supabase";

type FlashcardSet = Tables<"flashcard_sets"> & {
  card_count: number;
};

interface FlashcardSetListProps {
  onSelectSet: (setId: string, setName: string) => void; // Callback to handle when a set is selected
}

export const FlashcardSetList: React.FC<FlashcardSetListProps> = ({
  onSelectSet,
}) => {
  const { user } = useAuth();
  const [sets, setSets] = useState<FlashcardSet[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFlashcardSets = async () => {
      if (!user) {
        setLoading(false);
        return;      }
      setLoading(true);
      
      try {
        // First get the flashcard sets
        const { data: setsData, error: setsError } = await supabase
          .from("flashcard_sets")
          .select("*")
          .eq("user_id", user.id)
          .order("created_at", { ascending: false });

        if (setsError) throw setsError;

        // Then get the card counts for each set
        const setsWithCounts = await Promise.all(
          (setsData || []).map(async (set) => {
            const { count, error: countError } = await supabase
              .from("flashcards")
              .select("*", { count: "exact", head: true })
              .eq("set_id", set.id);

            if (countError) {
              console.error(`Error counting cards for set ${set.id}:`, countError);
              return { ...set, card_count: 0 };
            }

            return { ...set, card_count: count || 0 };
          })
        );
        
        setSets(setsWithCounts);
      } catch (err: any) {
        setError(err.message || "Failed to fetch flashcard sets.");
      } finally {
        setLoading(false);
      }
    };

    fetchFlashcardSets();

    const channel = supabase
      .channel("flashcard_sets_list_changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "flashcard_sets",
          filter: `user_id=eq.${user?.id}`,
        },
        (payload) => {
          console.log("Flashcard set change received!", payload);
          fetchFlashcardSets();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user]);

  const handleDeleteSet = async (setId: string) => {
    if (
      !window.confirm(
        "Are you sure you want to delete this flashcard set? All cards in this set will also be deleted."
      )
    )
      return;
    try {
      const { error } = await supabase
        .from("flashcard_sets")
        .delete()
        .match({ id: setId, user_id: user?.id });
      if (error) throw error;
      setSets(sets.filter((s) => s.id !== setId));
      alert("Flashcard set deleted.");
    } catch (err: any) {
      alert(`Error deleting set: ${err.message}`);
    }
  };

  if (loading)
    return <p className="text-slate-400 p-4">Loading flashcard sets...</p>;
  if (error) return <p className="text-red-400 p-4">Error: {error}</p>;

  return (
    <div className="mt-5 p-4 bg-slate-800 border border-slate-700 shadow-md rounded-lg text-slate-200">
      <h4 className="text-lg font-semibold mb-3 text-purple-400">
        Your Flashcard Sets
      </h4>      {sets.length === 0 ? (
        <p className="text-slate-300">No flashcard sets yet. Create one!</p>
      ) : (
        <ul className="space-y-4">
          {sets.map((set) => (
            <li
              key={set.id}
              className="p-4 border border-slate-700 rounded-md hover:bg-slate-700/60"
            >
              <div className="space-y-3">
                {/* Set title and description */}
                <div>
                  <strong
                    className="text-purple-400 cursor-pointer hover:text-purple-300 hover:underline text-lg"
                    onClick={() => onSelectSet(set.id, set.name)}
                  >
                    {set.name}
                  </strong>
                  <p className="text-sm text-slate-400 mt-1">
                    {set.description || "No description"}
                  </p>
                  <p className="text-xs text-slate-500 mt-1">
                    Created: {new Date(set.created_at!).toLocaleDateString()}
                  </p>
                </div>

                {/* Card count */}
                <div className="text-sm text-slate-300">
                  {set.card_count} {set.card_count === 1 ? 'Card' : 'Cards'}
                </div>

                {/* Action buttons */}
                <div className="flex gap-2">
                  <button
                    onClick={() => handleDeleteSet(set.id)}
                    className="w-20 h-8 text-xs border border-red-500 text-red-400 hover:bg-red-500/20 hover:text-red-300 rounded transition-colors flex items-center justify-center"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};
