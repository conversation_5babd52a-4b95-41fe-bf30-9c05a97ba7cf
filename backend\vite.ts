import express, { type Express } from "express";
import fs from "fs";
import path from "path";
import { createServer as createViteServer } from "vite";
import { fileURLToPath } from "url";

// Get current file's directory in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export function log(message: string) {
  const now = new Date();
  const timeStr = now.toLocaleTimeString();
  console.log(`${timeStr} [express] ${message}`);
}

export async function setupVite(app: Express) {
  if (process.env.NODE_ENV === "production") {
    log("Production mode detected, skipping Vite setup");
    return;
  }

  log("Setting up Vite dev middleware...");
  
  try {
    const vite = await createViteServer({
      server: { middlewareMode: true },
      appType: "spa",
      // Pass any additional Vite config options
    });

    app.use(vite.middlewares);
    log("Vite dev middleware set up successfully");
  } catch (e: any) {
    log(`Error setting up Vite: ${e.message}`);
    if (e.stack) log(e.stack);
    process.exit(1);
  }
}

export function serveStatic(app: Express) {
  // In production, serve the built frontend from the dist directory
  const distPath = path.resolve(process.cwd(), "dist");
  
  // Check if the dist directory exists
  if (!fs.existsSync(distPath)) {
    log(`Warning: Static files directory ${distPath} does not exist!`);
    log('Make sure to build the client first with npm run build:client');
    return;
  }
  
  log(`Serving static files from ${distPath}`);
  
  // Serve static files with caching for production
  app.use(express.static(distPath, {
    maxAge: process.env.NODE_ENV === 'production' ? '1y' : 0,
    etag: true,
    index: false // Don't automatically serve index.html, we'll handle that below
  }));

  // For SPA routing - all non-API routes should serve the index.html
  app.get("*", (req, res, next) => {
    // Skip API routes and let them be handled by their respective handlers
    if (req.path.startsWith("/api")) {
      return next();
    }
    
    // Serve the index.html for client-side routing
    const indexPath = path.join(distPath, "index.html");
    if (fs.existsSync(indexPath)) {
      res.sendFile(indexPath);
    } else {
      log(`Warning: index.html not found at ${indexPath}`);
      res.status(404).send('Frontend build not found');
    }
  });
}
