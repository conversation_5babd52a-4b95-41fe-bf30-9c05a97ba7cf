import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "./useAuth";
import { Tables } from "@/types/supabase";

type StudyDocument = Tables<"study_documents">;

export const useDocuments = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ["study-documents", user?.id],
    queryFn: async (): Promise<StudyDocument[]> => {
      if (!user) return [];

      console.log("useDocuments: Fetching documents for user:", user.id);

      const { data, error } = await supabase
        .from("study_documents")
        .select("*")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching documents:", error);
        throw error;
      }

      console.log("useDocuments: Fetched documents:", data);
      return data || [];
    },
    enabled: !!user,
  });
};
