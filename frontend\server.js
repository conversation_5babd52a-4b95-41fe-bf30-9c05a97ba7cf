import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const port = process.env.PORT || 3000;

// Serve static files from the dist directory
app.use(express.static(path.join(__dirname, 'dist')));

// API proxy middleware - forward API requests to backend
app.use('/api/*', (req, res) => {
  const backendUrl = process.env.BACKEND_URL || 'http://localhost:5000';
  const apiUrl = `${backendUrl}${req.originalUrl}`;
  
  console.log(`Proxying ${req.method} ${req.originalUrl} to ${apiUrl}`);
  
  // Simple proxy implementation
  const options = {
    method: req.method,
    headers: {
      ...req.headers,
      host: new URL(backendUrl).host,
    },
  };

  if (req.method !== 'GET' && req.method !== 'HEAD') {
    options.body = JSON.stringify(req.body);
    options.headers['content-type'] = 'application/json';
  }

  fetch(apiUrl, options)
    .then(response => {
      res.status(response.status);
      
      // Copy headers from backend response
      response.headers.forEach((value, key) => {
        res.setHeader(key, value);
      });
      
      return response.text();
    })
    .then(data => {
      res.send(data);
    })
    .catch(error => {
      console.error('Proxy error:', error);
      res.status(500).json({ error: 'Proxy error', message: error.message });
    });
});

// Handle React Router - serve index.html for all non-API routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

app.listen(port, '0.0.0.0', () => {
  console.log(`✅ Frontend server running on port ${port}`);
  console.log(`🌐 Frontend available at: http://localhost:${port}`);
  console.log(`🔗 API proxy target: ${process.env.BACKEND_URL || 'http://localhost:5000'}`);
});
