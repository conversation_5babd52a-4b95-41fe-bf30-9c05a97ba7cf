import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Edit, Trash2, RotateCcw } from "lucide-react";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "@/hooks/useAuth";
import { Tables } from "@/types/supabase";
import { useToast } from "@/hooks/use-toast";

type Flashcard = Tables<"flashcards">;

interface FlashcardsListProps {
  flashcards: Flashcard[];
  loading: boolean;
  onRefreshFlashcards: () => void;
  onEditFlashcard: (flashcard: Flashcard) => void;
  selectedDeckId: string;
}

const FlashcardsList: React.FC<FlashcardsListProps> = ({
  flashcards,
  loading,
  onRefreshFlashcards,
  onEditFlashcard,
  selectedDeckId,
}) => {
  const { user } = useAuth();
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const { toast } = useToast();

  const handleDelete = async (flashcard: Flashcard) => {
    if (!confirm(`Are you sure you want to delete this flashcard?`)) {
      return;
    }

    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to delete flashcards.",
        variant: "destructive",
      });
      return;
    }

    setDeletingId(flashcard.id);
    try {
      const { error } = await supabase
        .from("flashcards")
        .delete()
        .eq("id", flashcard.id)
        .eq("user_id", user.id);

      if (error) throw error;

      onRefreshFlashcards();
      toast({
        title: "Success",
        description: "Flashcard deleted successfully.",
      });
    } catch (error: any) {
      console.error("Error deleting flashcard:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete flashcard.",
        variant: "destructive",
      });
    } finally {
      setDeletingId(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getSRSStatus = (flashcard: Flashcard) => {
    if (!flashcard.review_count || flashcard.review_count === 0) {
      return { label: "New", color: "bg-blue-500" };
    }

    const now = new Date();
    if (flashcard.due_at && new Date(flashcard.due_at) <= now) {
      return { label: "Due", color: "bg-red-500" };
    }

    if (flashcard.interval_days && flashcard.interval_days >= 30) {
      return { label: "Mastered", color: "bg-green-500" };
    }

    return { label: "Learning", color: "bg-yellow-500" };
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-24 bg-slate-700 rounded-lg"></div>
          </div>
        ))}
      </div>
    );
  }

  if (flashcards.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-slate-400 mb-4">
          <RotateCcw className="h-12 w-12 mx-auto mb-2 opacity-50" />
          <p>No flashcards found in this deck.</p>
          <p className="text-sm">Add some flashcards to get started!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <p className="text-sm text-slate-400">
          {flashcards.length} flashcard{flashcards.length !== 1 ? "s" : ""}
        </p>
        <Button
          variant="outline"
          size="sm"
          onClick={onRefreshFlashcards}
          className="border-slate-600 text-slate-300 hover:bg-slate-700"
        >
          <RotateCcw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <div className="space-y-3">
        {flashcards.map((flashcard, index) => {
          const srsStatus = getSRSStatus(flashcard);
          
          return (
            <Card
              key={flashcard.id}
              className="bg-slate-700/50 border-slate-600 hover:bg-slate-700/70 transition-colors"
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-slate-400 font-mono text-sm bg-slate-800 px-2 py-1 rounded">
                        #{index + 1}
                      </span>
                      <span
                        className={`${srsStatus.color} text-white text-xs px-2 py-1 rounded-full`}
                      >
                        {srsStatus.label}
                      </span>
                      <span className="text-xs text-slate-500">
                        Created: {formatDate(flashcard.created_at)}
                      </span>
                    </div>

                    <div className="space-y-2">
                      <div>
                        <p className="text-xs text-slate-400 mb-1">Question:</p>
                        <p className="text-slate-200 text-sm line-clamp-2">
                          {flashcard.front_text}
                        </p>
                      </div>

                      <div>
                        <p className="text-xs text-slate-400 mb-1">Answer:</p>
                        <p className="text-slate-300 text-sm line-clamp-2">
                          {flashcard.back_text}
                        </p>
                      </div>
                    </div>

                    {flashcard.review_count && flashcard.review_count > 0 && (
                      <div className="mt-2 text-xs text-slate-500">
                        Reviews: {flashcard.review_count} |
                        Correct: {flashcard.correct_count || 0} |
                        Incorrect: {flashcard.incorrect_count || 0}
                        {flashcard.due_at && (
                          <> | Next: {formatDate(flashcard.due_at)}</>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onEditFlashcard(flashcard)}
                      className="border-slate-600 text-slate-300 hover:bg-slate-600"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(flashcard)}
                      disabled={deletingId === flashcard.id}
                      className="border-red-600 text-red-400 hover:bg-red-900/20"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default FlashcardsList;
