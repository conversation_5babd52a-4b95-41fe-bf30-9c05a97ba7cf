import React, { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { Upload, FileText, AlertCircle, CheckCircle, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { extractTextFromFile } from "@/lib/file-parser";
import { uploadDocumentSecure } from "@/lib/api";
import { notify, commonNotifications } from "@/lib/notifications";

interface UploadProgress {
  stage: "parsing" | "uploading" | "complete" | "error";
  progress: number;
  message: string;
}

interface FileUploadItem {
  file: File;
  id: string;
  progress: UploadProgress;
  result?: any;
  error?: string;
}

interface EnhancedDocumentUploadProps {
  onUploadComplete?: (documents: any[]) => void;
  onClose?: () => void;
  maxFiles?: number;
  maxFileSize?: number;
}

const SUPPORTED_TYPES = {
  "application/pdf": ".pdf",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
    ".docx",
  "text/plain": ".txt",
  "text/markdown": ".md",
};

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export const EnhancedDocumentUpload: React.FC<EnhancedDocumentUploadProps> = ({
  onUploadComplete,
  onClose,
  maxFiles = 5,
  maxFileSize = MAX_FILE_SIZE,
}) => {
  const [uploadItems, setUploadItems] = useState<FileUploadItem[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const onDrop = useCallback(
    (acceptedFiles: File[], rejectedFiles: any[]) => {
      if (rejectedFiles.length > 0) {
        const errors = rejectedFiles.map(
          (file) =>
            `${file.file.name}: ${file.errors[0]?.message || "Invalid file"}`
        );
        notify.error({
          title: "Some files were rejected",
          description: errors.join(", "),
        });
      }

      if (uploadItems.length + acceptedFiles.length > maxFiles) {
        notify.warning({
          title: "Too many files",
          description: `You can only upload up to ${maxFiles} files at once.`,
        });
        return;
      }

      const newItems: FileUploadItem[] = acceptedFiles.map((file) => ({
        file,
        id: crypto.randomUUID(),
        progress: {
          stage: "parsing",
          progress: 0,
          message: "Preparing to extract text...",
        },
      }));

      setUploadItems((prev) => [...prev, ...newItems]);
      processFiles(newItems);
    },
    [uploadItems.length, maxFiles]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: SUPPORTED_TYPES,
    maxSize: maxFileSize,
    maxFiles,
    multiple: true,
  });

  const updateProgress = (id: string, progress: Partial<UploadProgress>) => {
    setUploadItems((prev) =>
      prev.map((item) =>
        item.id === id
          ? { ...item, progress: { ...item.progress, ...progress } }
          : item
      )
    );
  };

  const processFiles = async (items: FileUploadItem[]) => {
    setIsUploading(true);
    const results = [];

    for (const item of items) {
      try {
        updateProgress(item.id, {
          stage: "parsing",
          progress: 10,
          message: "Extracting text from document...",
        });

        const document = await extractTextFromFile(item.file);

        updateProgress(item.id, {
          stage: "parsing",
          progress: 50,
          message: "Text extraction complete",
        });

        updateProgress(item.id, {
          stage: "uploading",
          progress: 60,
          message: "Uploading to secure storage...",
        });

        const uploadResponse = await uploadDocumentSecure({
          fileName: document.name,
          content: document.content,
          contentType: document.type,
          sizeBytes: document.size,
          documentId: document.id,
        });

        updateProgress(item.id, {
          stage: "complete",
          progress: 100,
          message: "Upload successful",
        });

        results.push(uploadResponse.document);

        setUploadItems((prev) =>
          prev.map((uploadItem) =>
            uploadItem.id === item.id
              ? { ...uploadItem, result: uploadResponse.document }
              : uploadItem
          )
        );
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Upload failed";

        updateProgress(item.id, {
          stage: "error",
          progress: 0,
          message: errorMessage,
        });

        setUploadItems((prev) =>
          prev.map((uploadItem) =>
            uploadItem.id === item.id
              ? { ...uploadItem, error: errorMessage }
              : uploadItem
          )
        );

        commonNotifications.documentUploadFailed(errorMessage);
      }
    }

    setIsUploading(false);

    if (results.length > 0) {
      notify.success({
        title: "Documents uploaded",
        description: `Successfully uploaded ${results.length} document${
          results.length > 1 ? "s" : ""
        }.`,
      });
      onUploadComplete?.(results);
    }
  };

  const removeItem = (id: string) => {
    setUploadItems((prev) => prev.filter((item) => item.id !== id));
  };

  const getProgressColor = (stage: UploadProgress["stage"]) => {
    switch (stage) {
      case "complete":
        return "bg-green-500";
      case "error":
        return "bg-red-500";
      case "uploading":
        return "bg-blue-500";
      default:
        return "bg-purple-500";
    }
  };

  const getFileIcon = (fileName: string) => {
    if (fileName.endsWith(".pdf")) return "📄";
    if (fileName.endsWith(".docx")) return "📝";
    if (fileName.endsWith(".txt")) return "📝";
    if (fileName.endsWith(".md")) return "📄";
    return "📄";
  };

  return (
    <div className="space-y-6">
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
              ${
                isDragActive
                  ? "border-purple-400 bg-purple-950/20"
                  : "border-slate-600 hover:border-purple-500 hover:bg-slate-750"
              }
            `}
          >
            <input {...getInputProps()} />
            <Upload className="mx-auto h-12 w-12 text-slate-400 mb-4" />

            {isDragActive ? (
              <p className="text-purple-400 text-lg font-medium">
                Drop files here to upload
              </p>
            ) : (
              <>
                <h3 className="text-lg font-medium text-slate-200 mb-2">
                  Upload Study Documents
                </h3>
                <p className="text-slate-400 mb-4">
                  Drag and drop files here, or click to browse
                </p>
                <div className="flex flex-wrap justify-center gap-2 mb-4">
                  {Object.values(SUPPORTED_TYPES).map((ext) => (
                    <Badge
                      key={ext}
                      variant="secondary"
                      className="bg-slate-700 text-slate-300"
                    >
                      {ext}
                    </Badge>
                  ))}
                </div>
                <p className="text-sm text-slate-500">
                  Max {maxFiles} files, up to{" "}
                  {Math.round(maxFileSize / 1024 / 1024)}MB each
                </p>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {uploadItems.length > 0 && (
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <h4 className="text-lg font-medium text-slate-200 mb-4">
              Upload Progress
            </h4>

            <div className="space-y-4">
              {uploadItems.map((item) => (
                <div key={item.id} className="bg-slate-900 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <span className="text-xl">
                        {getFileIcon(item.file.name)}
                      </span>
                      <div>
                        <p className="text-slate-200 font-medium">
                          {item.file.name}
                        </p>
                        <p className="text-sm text-slate-400">
                          {(item.file.size / 1024).toFixed(1)} KB
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {item.progress.stage === "complete" && (
                        <CheckCircle className="h-5 w-5 text-green-400" />
                      )}
                      {item.progress.stage === "error" && (
                        <AlertCircle className="h-5 w-5 text-red-400" />
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeItem(item.id)}
                        className="text-slate-400 hover:text-slate-200"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-slate-300">
                        {item.progress.message}
                      </span>
                      <span className="text-slate-400">
                        {item.progress.progress}%
                      </span>
                    </div>
                    <Progress value={item.progress.progress} className="h-2" />
                  </div>

                  {item.error && (
                    <p className="text-red-400 text-sm mt-2">{item.error}</p>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex justify-end space-x-3">
        {onClose && (
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isUploading}
            className="text-slate-300 border-slate-600"
          >
            Close
          </Button>
        )}

        <Button
          onClick={() => setUploadItems([])}
          variant="outline"
          disabled={isUploading || uploadItems.length === 0}
          className="text-slate-300 border-slate-600"
        >
          Clear All
        </Button>
      </div>
    </div>
  );
};
