import React, { useState, FormEvent } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Terminal } from "lucide-react";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "@/hooks/useAuth";
import { Tables, TablesInsert } from "@/types/supabase";

type Flashcard = Tables<"flashcards">;
type FlashcardInsert = TablesInsert<"flashcards">;

interface FlashcardFormProps {
  selectedDeckId: string;
  editingFlashcard?: Flashcard | null;
  onFlashcardSaved: () => void;
  onCancel: () => void;
}

const FlashcardForm: React.FC<FlashcardFormProps> = ({
  selectedDeckId,
  editingFlashcard,
  onFlashcardSaved,
  onCancel,
}) => {
  const { user } = useAuth();
  const [question, setQuestion] = useState(editingFlashcard?.front_text || "");
  const [answer, setAnswer] = useState(editingFlashcard?.back_text || "");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!question.trim() || !answer.trim()) {
      setError("Both question and answer are required.");
      return;
    }

    if (!user) {
      setError("You must be logged in to save flashcards.");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      if (editingFlashcard) {
        // Update existing flashcard
        const { error: updateError } = await supabase
          .from("flashcards")
          .update({
            front_text: question.trim(),
            back_text: answer.trim(),
            updated_at: new Date().toISOString(),
          })
          .eq("id", editingFlashcard.id)
          .eq("user_id", user.id);

        if (updateError) throw updateError;
      } else {
        // Create new flashcard
        const newFlashcard: FlashcardInsert = {
          set_id: selectedDeckId,
          user_id: user.id,
          front_text: question.trim(),
          back_text: answer.trim(),
        };

        const { error: insertError } = await supabase
          .from("flashcards")
          .insert(newFlashcard);

        if (insertError) throw insertError;
      }

      onFlashcardSaved();

      // Reset form if adding new
      if (!editingFlashcard) {
        setQuestion("");
        setAnswer("");
      }
    } catch (err: any) {
      console.error("Error saving flashcard:", err);
      setError(
        err.message || "Failed to save flashcard."
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="flex items-center justify-between border-b border-slate-700 pb-4">
        <h5 className="text-lg font-bold text-slate-200 flex items-center">
          {editingFlashcard ? (
            <>
              <svg
                className="w-5 h-5 mr-2 text-yellow-400"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
              </svg>
              Edit Flashcard
            </>
          ) : (
            <>
              <svg
                className="w-5 h-5 mr-2 text-green-400"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                  clipRule="evenodd"
                />
              </svg>
              Add New Flashcard
            </>
          )}
        </h5>
        {editingFlashcard && (
          <Button
            type="button"
            variant="ghost"
            onClick={onCancel}
            className="text-slate-400 hover:text-slate-300"
          >
            Cancel
          </Button>
        )}
      </div>

      {error && (
        <Alert variant="destructive">
          <Terminal className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-4">
        <div>
          <Label htmlFor="question" className="text-slate-300">
            Question (Front of Card)
          </Label>
          <Textarea
            id="question"
            value={question}
            onChange={(e) => setQuestion(e.target.value)}
            placeholder="Enter the question or prompt..."
            rows={3}
            className="mt-1 w-full p-3 border rounded-md bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus:ring-purple-500 focus:border-purple-500"
            disabled={loading}
            required
          />
        </div>

        <div>
          <Label htmlFor="answer" className="text-slate-300">
            Answer (Back of Card)
          </Label>
          <Textarea
            id="answer"
            value={answer}
            onChange={(e) => setAnswer(e.target.value)}
            placeholder="Enter the answer or explanation..."
            rows={3}
            className="mt-1 w-full p-3 border rounded-md bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus:ring-purple-500 focus:border-purple-500"
            disabled={loading}
            required
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
          className="border-slate-600 text-slate-300 hover:bg-slate-700"
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={loading || !question.trim() || !answer.trim()}
          className="bg-purple-600 hover:bg-purple-700 text-white"
        >
          {loading
            ? editingFlashcard
              ? "Saving..."
              : "Adding..."
            : editingFlashcard
            ? "Save Changes"
            : "Add Flashcard"}
        </Button>
      </div>
    </form>
  );
};

export default FlashcardForm;
