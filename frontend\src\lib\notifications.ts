import { toast } from "@/hooks/use-toast";

export interface NotificationOptions {
  title: string;
  description?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export class NotificationManager {
  static success(options: NotificationOptions) {
    toast({
      title: options.title,
      description: options.description,
      duration: options.duration || 4000,
      className: "border-green-500 bg-green-950 text-green-100",
      action: options.action
        ? {
            altText: options.action.label,
            onClick: options.action.onClick,
            children: options.action.label,
          }
        : undefined,
    });
  }

  static error(options: NotificationOptions) {
    toast({
      title: options.title,
      description: options.description,
      duration: options.duration || 6000,
      variant: "destructive",
      action: options.action
        ? {
            altText: options.action.label,
            onClick: options.action.onClick,
            children: options.action.label,
          }
        : undefined,
    });
  }

  static warning(options: NotificationOptions) {
    toast({
      title: options.title,
      description: options.description,
      duration: options.duration || 5000,
      className: "border-yellow-500 bg-yellow-950 text-yellow-100",
      action: options.action
        ? {
            altText: options.action.label,
            onClick: options.action.onClick,
            children: options.action.label,
          }
        : undefined,
    });
  }

  static info(options: NotificationOptions) {
    toast({
      title: options.title,
      description: options.description,
      duration: options.duration || 4000,
      className: "border-blue-500 bg-blue-950 text-blue-100",
      action: options.action
        ? {
            altText: options.action.label,
            onClick: options.action.onClick,
            children: options.action.label,
          }
        : undefined,
    });
  }

  static loading(options: Omit<NotificationOptions, "duration">) {
    return toast({
      title: options.title,
      description: options.description,
      duration: Infinity,
      className: "border-purple-500 bg-purple-950 text-purple-100",
    });
  }

  static dismiss(toastId?: string) {
    if (toastId) {
      // Individual toast dismissal would need to be implemented in the toast system
      console.log("Dismissing toast:", toastId);
    }
  }
}

export const notify = {
  success: NotificationManager.success,
  error: NotificationManager.error,
  warning: NotificationManager.warning,
  info: NotificationManager.info,
  loading: NotificationManager.loading,
  dismiss: NotificationManager.dismiss,
};

export const commonNotifications = {
  documentUploaded: (fileName: string) =>
    notify.success({
      title: "Document uploaded successfully",
      description: `${fileName} has been processed and is ready for study.`,
    }),

  documentUploadFailed: (error: string) =>
    notify.error({
      title: "Document upload failed",
      description: error,
      action: {
        label: "Retry",
        onClick: () => window.location.reload(),
      },
    }),

  flashcardsGenerated: (count: number) =>
    notify.success({
      title: "Flashcards generated",
      description: `Successfully created ${count} flashcards for your study.`,
    }),

  flashcardGenerationFailed: (error: string) =>
    notify.error({
      title: "Flashcard generation failed",
      description: error,
    }),

  quizGenerated: (name: string) =>
    notify.success({
      title: "Quiz generated",
      description: `"${name}" is ready for you to take.`,
    }),

  quizGenerationFailed: (error: string) =>
    notify.error({
      title: "Quiz generation failed",
      description: error,
    }),

  aiConfigSaved: () =>
    notify.success({
      title: "AI configuration saved",
      description: "Your AI provider settings have been updated.",
    }),

  aiConfigFailed: (error: string) =>
    notify.error({
      title: "Failed to save AI configuration",
      description: error,
    }),

  dataExported: (fileName: string) =>
    notify.success({
      title: "Data exported",
      description: `Your study data has been exported to ${fileName}.`,
    }),

  dataExportFailed: (error: string) =>
    notify.error({
      title: "Export failed",
      description: error,
    }),

  sessionExpired: () =>
    notify.warning({
      title: "Session expired",
      description: "Please sign in again to continue.",
      action: {
        label: "Sign In",
        onClick: () => (window.location.href = "/login"),
      },
    }),

  networkError: () =>
    notify.error({
      title: "Network error",
      description: "Please check your internet connection and try again.",
      action: {
        label: "Retry",
        onClick: () => window.location.reload(),
      },
    }),

  comingSoon: (feature: string) =>
    notify.info({
      title: "Coming soon",
      description: `${feature} is currently under development.`,
    }),
};
