import React, { useEffect, useState } from "react";
import { supabase } from "../../lib/supabaseClient";
import { useAuth } from "../../hooks/useAuth";
import { Tables } from "../../types/supabase";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Brain,
  TrendingUp,
  Clock,
  Target,
  Calendar,
  PlayCircle,
  BarChart3,
} from "lucide-react";
import { getQuizQuestionsDueForReview, getQuizQuestionsDueCount } from "@/lib/srs";
import { SRSQuizMode } from "../quiz/SRSQuizMode";

type QuizQuestion = Tables<"quiz_questions">;

interface SRSStats {
  totalQuestions: number;
  dueForReview: number;
  reviewedToday: number;
  averageSRSLevel: number;
  nextReviewDate: string | null;
  streakDays: number;
}

export const SRSDashboard: React.FC = () => {
  const { user } = useAuth();
  const [questions, setQuestions] = useState<QuizQuestion[]>([]);
  const [stats, setStats] = useState<SRSStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [showSRSMode, setShowSRSMode] = useState(false);

  useEffect(() => {
    const fetchSRSData = async () => {
      if (!user) return;
      
      setLoading(true);
      try {
        const { data: questionsData, error } = await supabase
          .from("quiz_questions")
          .select("*")
          .eq("user_id", user.id)
          .order("created_at", { ascending: true });

        if (error) throw error;

        const allQuestions = questionsData || [];
        setQuestions(allQuestions);

        // Calculate SRS statistics
        const dueQuestions = getQuizQuestionsDueForReview(allQuestions);
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const reviewedToday = allQuestions.filter(q => {
          if (!q.last_reviewed_at) return false;
          const reviewDate = new Date(q.last_reviewed_at);
          reviewDate.setHours(0, 0, 0, 0);
          return reviewDate.getTime() === today.getTime();
        }).length;

        const averageSRSLevel = allQuestions.length > 0
          ? allQuestions.reduce((sum, q) => sum + (q.srs_level || 0), 0) / allQuestions.length
          : 0;

        // Find next review date
        const futureReviews = allQuestions
          .filter(q => q.due_at && new Date(q.due_at) > new Date())
          .sort((a, b) => new Date(a.due_at!).getTime() - new Date(b.due_at!).getTime());

        const nextReviewDate = futureReviews.length > 0 ? futureReviews[0].due_at : null;

        // Calculate streak (simplified - days with reviews in the last week)
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        const recentReviews = allQuestions.filter(q => 
          q.last_reviewed_at && new Date(q.last_reviewed_at) >= weekAgo
        );
        const uniqueReviewDays = new Set(
          recentReviews.map(q => new Date(q.last_reviewed_at!).toDateString())
        );

        setStats({
          totalQuestions: allQuestions.length,
          dueForReview: dueQuestions.length,
          reviewedToday,
          averageSRSLevel: Math.round(averageSRSLevel * 10) / 10,
          nextReviewDate,
          streakDays: uniqueReviewDays.size,
        });

      } catch (error) {
        console.error("Error fetching SRS data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchSRSData();
  }, [user]);

  const formatNextReview = (dateString: string | null): string => {
    if (!dateString) return "No upcoming reviews";
    
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = date.getTime() - now.getTime();
    const diffHours = Math.ceil(diffMs / (1000 * 60 * 60));
    const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));

    if (diffHours < 1) return "Soon";
    if (diffHours < 24) return `${diffHours}h`;
    if (diffDays === 1) return "Tomorrow";
    if (diffDays < 7) return `${diffDays} days`;
    return date.toLocaleDateString();
  };

  if (showSRSMode) {
    return <SRSQuizMode onExit={() => setShowSRSMode(false)} />;
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-xl text-slate-200">
            <Brain className="h-6 w-6 text-purple-400" />
            Spaced Repetition System
          </CardTitle>
          <p className="text-slate-400 text-sm">
            Optimize your learning with scientifically-proven spaced repetition
          </p>
        </CardHeader>
        <CardContent>
          {stats && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-slate-900 p-4 rounded-lg text-center">
                <div className="flex items-center justify-center mb-2">
                  <Target className="h-5 w-5 text-purple-400" />
                </div>
                <p className="text-slate-400 text-sm">Total Questions</p>
                <p className="text-2xl font-bold text-purple-400">
                  {stats.totalQuestions}
                </p>
              </div>

              <div className="bg-slate-900 p-4 rounded-lg text-center">
                <div className="flex items-center justify-center mb-2">
                  <Clock className="h-5 w-5 text-orange-400" />
                </div>
                <p className="text-slate-400 text-sm">Due for Review</p>
                <p className="text-2xl font-bold text-orange-400">
                  {stats.dueForReview}
                </p>
              </div>

              <div className="bg-slate-900 p-4 rounded-lg text-center">
                <div className="flex items-center justify-center mb-2">
                  <TrendingUp className="h-5 w-5 text-green-400" />
                </div>
                <p className="text-slate-400 text-sm">Avg. SRS Level</p>
                <p className="text-2xl font-bold text-green-400">
                  {stats.averageSRSLevel}
                </p>
              </div>

              <div className="bg-slate-900 p-4 rounded-lg text-center">
                <div className="flex items-center justify-center mb-2">
                  <BarChart3 className="h-5 w-5 text-blue-400" />
                </div>
                <p className="text-slate-400 text-sm">Study Streak</p>
                <p className="text-2xl font-bold text-blue-400">
                  {stats.streakDays} days
                </p>
              </div>
            </div>
          )}

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              onClick={() => setShowSRSMode(true)}
              disabled={!stats || stats.dueForReview === 0}
              className="bg-purple-600 hover:bg-purple-700 flex-1 sm:flex-none"
            >
              <PlayCircle className="h-4 w-4 mr-2" />
              Start SRS Review
              {stats && stats.dueForReview > 0 && (
                <Badge variant="secondary" className="ml-2 bg-orange-900/30 text-orange-400">
                  {stats.dueForReview}
                </Badge>
              )}
            </Button>

            {stats && stats.nextReviewDate && (
              <div className="flex items-center justify-center text-sm text-slate-400">
                <Calendar className="h-4 w-4 mr-2" />
                Next review: {formatNextReview(stats.nextReviewDate)}
              </div>
            )}
          </div>

          {stats && stats.dueForReview === 0 && (
            <div className="text-center mt-4">
              <p className="text-slate-400 text-sm">
                🎉 All caught up! No questions are due for review right now.
              </p>
              {stats.nextReviewDate && (
                <p className="text-slate-500 text-xs mt-1">
                  Next review in {formatNextReview(stats.nextReviewDate)}
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {stats && stats.totalQuestions === 0 && (
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6 text-center">
            <Brain className="h-16 w-16 mx-auto text-slate-600 mb-4" />
            <h3 className="text-lg font-medium text-slate-200 mb-2">
              No Questions Yet
            </h3>
            <p className="text-slate-400 mb-4">
              Create some quizzes to start using the spaced repetition system!
            </p>
            <p className="text-slate-500 text-sm">
              The SRS will help you review questions at optimal intervals to maximize long-term retention.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}; 