import express, { Request, Response, Router } from "express";
import {
  GenerateFlashcardsRequest,
  GenerateFlashcardsResponse,
  FlashcardDeck,
} from "../../shared/types/flashcards";
// We will need a library to interact with AI providers, e.g., a generic OpenAI-compatible client
// For now, let's assume an aiServiceClient exists or will be created.
// import { aiServiceClient } from '../services/aiServiceClient';
import { getAIProviderSettings } from "../../client/src/lib/ai-provider"; // This is client-side, need a backend equivalent
import { v4 as uuidv4 } from "uuid";

const router: Router = express.Router();

// Placeholder for backend AI provider configuration management
// In a real app, this would be securely managed, possibly from environment variables or a secure store
// and not rely on client-side logic.
// For MVP, per PRD, user-provided keys are handled ephemerally.

interface BackendAIProviderSettings {
  provider: string;
  baseUrl: string;
  apiKey: string; // User-provided, handled ephemerally
  model: string;
}

const getBackendAIProviderConfig = (
  userApiKey: string,
  userBaseUrl?: string,
  userModel?: string
): BackendAIProviderSettings => {
  // PRD: "Backend securely handles user-provided AI API credentials (Base URL, API Key, Model ID) ephemerally per request..."
  // PRD: "Defaults to OpenRouter using google/gemini-2.5-flash for extraction and google/gemini-2.5-pro for content generation."
  // The `getRecommendedModelForTask` from `ai-provider.ts` uses 'google/gemini-2.5-pro-preview' for generation.

  const openRouterBaseUrl = "https://openrouter.ai/api/v1";
  const defaultGenerationModel = "google/gemini-2.5-pro-preview"; // Aligning with client/src/lib/ai-provider.ts

  return {
    provider: "OpenRouter", // Default, but could be part of user config
    baseUrl: userBaseUrl || openRouterBaseUrl,
    apiKey: userApiKey, // This MUST be provided by the user with each request for ephemeral handling
    model: userModel || defaultGenerationModel,
  };
};

router.post("/generate-flashcards", async (req: Request, res: Response) => {
  const { textContent, documentId, deckTitle } =
    req.body as GenerateFlashcardsRequest;

  // IMPORTANT: Retrieve user's API key from a secure header or session.
  // For this example, let's assume it's passed in a custom header 'X-User-AI-Key'.
  // In a real application, ensure this is handled securely and ephemerally.
  const userApiKey = req.headers["x-user-ai-key"] as string;
  const userApiBaseUrl = req.headers["x-user-ai-base-url"] as
    | string
    | undefined;
  const userApiModel = req.headers["x-user-ai-model"] as string | undefined;

  if (!textContent) {
    return res.status(400).json({ error: "textContent is required." });
  }
  if (!documentId) {
    return res.status(400).json({ error: "documentId is required." });
  }
  if (!userApiKey) {
    return res
      .status(400)
      .json({ error: "User AI API key (X-User-AI-Key header) is required." });
  }

  try {
    const aiConfig = getBackendAIProviderConfig(
      userApiKey,
      userApiBaseUrl,
      userApiModel
    );

    console.log(
      `Generating flashcards for document ${documentId} using model ${aiConfig.model} via ${aiConfig.baseUrl}`
    );

    // Construct the prompt for the AI
    // This is a crucial step and needs to be well-crafted.
    const prompt = `
            Based on the following text, generate a set of flashcards.
            Each flashcard should have a clear "question" and a concise "answer".
            Format the output as a JSON array of objects, where each object has a "question" and "answer" key.
            For example: [{"question": "What is the capital of France?", "answer": "Paris"}, {"question": "...", "answer": "..."}]
            Ensure the questions are distinct and cover key concepts from the text.
            Do not include any introductory text or explanations, only the JSON array.

            Text:
            ---
            ${textContent}
            ---
        `;

    // Simulate AI interaction
    // In a real implementation, this would involve an HTTP call to the AI provider.
    // const aiResponse = await aiServiceClient.generate(prompt, aiConfig);

    // Placeholder for AI response - this needs to be replaced with actual AI call
    // For now, simulating a successful response with some example flashcards
    await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulate network delay
    const simulatedAiRawResponse = `
        [
            {"question": "What is the primary goal of ChewyAI according to the PRD?", "answer": "To provide students and lifelong learners with a powerful, secure, and customizable AI-driven study tool, optimized for local development and flexible deployment."},
            {"question": "What frontend technology is ChewyAI's browser application built with?", "answer": "React (using TypeScript)."},
            {"question": "What backend technology does ChewyAI use?", "answer": "Node.js/Express.js (TypeScript)."},
            {"question": "What is the default AI provider and models for ChewyAI?", "answer": "OpenRouter, with google/gemini-2.5-flash for extraction and google/gemini-2.5-pro for content generation (or gemini-2.5-pro-preview as per latest recommendation)."},
            {"question": "How are user-provided AI API keys handled by the ChewyAI backend?", "answer": "Ephemerally, per request or short-lived secure session. They are not stored persistently."}
        ]
        `;

    let parsedFlashcards: { question: string; answer: string }[];
    try {
      parsedFlashcards = JSON.parse(simulatedAiRawResponse.trim());
    } catch (parseError) {
      console.error("Failed to parse AI response:", parseError);
      console.error("Raw AI Response was:", simulatedAiRawResponse);
      return res
        .status(500)
        .json({ error: "Failed to parse flashcards from AI response." });
    }

    if (
      !Array.isArray(parsedFlashcards) ||
      !parsedFlashcards.every((fc) => fc.question && fc.answer)
    ) {
      console.error("Invalid flashcard structure from AI:", parsedFlashcards);
      return res
        .status(500)
        .json({ error: "AI returned an invalid flashcard structure." });
    }

    const newDeckId = uuidv4();
    const newDeck: FlashcardDeck = {
      id: newDeckId,
      documentId,
      title: deckTitle || `Flashcards for ${documentId}`,
      flashcards: parsedFlashcards.map((fc) => ({
        id: uuidv4(),
        question: fc.question,
        answer: fc.answer,
        deckId: newDeckId,
      })),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const response: GenerateFlashcardsResponse = { deck: newDeck };
    res.status(201).json(response);
  } catch (error) {
    console.error("Error generating flashcards:", error);
    // Differentiate between known errors and unexpected ones
    if (error instanceof Error) {
      res
        .status(500)
        .json({ error: `Internal server error: ${error.message}` });
    } else {
      res
        .status(500)
        .json({ error: "An unexpected internal server error occurred." });
    }
  }
});

export default router;
