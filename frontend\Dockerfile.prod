FROM node:20-alpine as builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy client code and necessary build files
COPY client/ ./client/
COPY shared/ ./shared/
COPY tsconfig.json ./
COPY vite.config.ts ./
COPY tailwind.config.ts ./
COPY postcss.config.js ./
COPY components.json ./

# Set the API URL for production
ENV VITE_API_BASE_URL=http://localhost:5000/api

# Build the client
RUN npm run build:client

# Production stage with Nginx
FROM nginx:alpine

# Copy the built files to nginx html directory
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY ./client/nginx.conf /etc/nginx/conf.d/default.conf

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
