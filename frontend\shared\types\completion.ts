// Types for completion tracking functionality

export interface UserCompletion {
  id: string;
  user_id: string;
  quiz_id?: string;
  flashcard_set_id?: string;
  completion_type: 'quiz' | 'flashcard_set';
  completed_at: string;
  score?: number; // Percentage (0-100) for quizzes
  time_spent_minutes?: number;
  questions_answered?: number;
  correct_answers?: number;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface UserCompletionInsert {
  user_id: string;
  quiz_id?: string;
  flashcard_set_id?: string;
  completion_type: 'quiz' | 'flashcard_set';
  completed_at?: string;
  score?: number;
  time_spent_minutes?: number;
  questions_answered?: number;
  correct_answers?: number;
  metadata?: Record<string, any>;
}

export interface QuizCompletionData {
  quiz_id: string;
  score: number;
  time_spent_minutes: number;
  questions_answered: number;
  correct_answers: number;
  metadata?: {
    difficulty?: string;
    srs_mode?: boolean;
    question_types?: string[];
    average_response_time?: number;
  };
}

export interface FlashcardSetCompletionData {
  flashcard_set_id: string;
  time_spent_minutes: number;
  metadata?: {
    cards_reviewed?: number;
    cards_correct?: number;
    cards_incorrect?: number;
    difficulty_rating?: number;
    review_mode?: string;
  };
}

export interface CompletionStats {
  total_completions: number;
  quiz_completions: number;
  flashcard_completions: number;
  average_quiz_score: number;
  total_study_time_minutes: number;
  best_quiz_score: number;
  recent_completions: UserCompletion[];
  completion_streak: number;
  this_week_completions: number;
  this_month_completions: number;
}

export interface CompletionFilters {
  completion_type?: 'quiz' | 'flashcard_set';
  date_range?: {
    start: string;
    end: string;
  };
  quiz_id?: string;
  flashcard_set_id?: string;
  min_score?: number;
  limit?: number;
  offset?: number;
}
