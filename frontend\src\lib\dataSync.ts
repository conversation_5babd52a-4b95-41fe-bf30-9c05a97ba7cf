import React from "react";
import localforage from "localforage";
import { getUserDocuments } from "./api";
import { getFlashcardDecks } from "./storage";
import { notify } from "./notifications";

export interface SyncStatus {
  lastSyncTime: number | null;
  isOnline: boolean;
  pendingUploads: number;
  pendingDownloads: number;
  syncInProgress: boolean;
}

export interface SyncOptions {
  forceSync?: boolean;
  backgroundSync?: boolean;
  onProgress?: (progress: number) => void;
  onStatusChange?: (status: SyncStatus) => void;
}

class DataSyncManager {
  private syncStatus: SyncStatus = {
    lastSyncTime: null,
    isOnline: navigator.onLine,
    pendingUploads: 0,
    pendingDownloads: 0,
    syncInProgress: false,
  };

  private statusCallbacks: Set<(status: SyncStatus) => void> = new Set();
  private syncInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.initializeEventListeners();
    this.loadSyncStatus();
    this.startPeriodicSync();
  }

  private initializeEventListeners() {
    window.addEventListener("online", () => {
      this.updateSyncStatus({ isOnline: true });
      this.triggerSync({ backgroundSync: true });
    });

    window.addEventListener("offline", () => {
      this.updateSyncStatus({ isOnline: false });
    });

    document.addEventListener("visibilitychange", () => {
      if (!document.hidden && this.syncStatus.isOnline) {
        this.triggerSync({ backgroundSync: true });
      }
    });
  }

  private async loadSyncStatus() {
    try {
      const stored = await localforage.getItem<SyncStatus>("sync-status");
      if (stored) {
        this.syncStatus = { ...this.syncStatus, ...stored };
      }
    } catch (error) {
      console.warn("Failed to load sync status:", error);
    }
  }

  private async saveSyncStatus() {
    try {
      await localforage.setItem("sync-status", this.syncStatus);
    } catch (error) {
      console.warn("Failed to save sync status:", error);
    }
  }

  private updateSyncStatus(updates: Partial<SyncStatus>) {
    this.syncStatus = { ...this.syncStatus, ...updates };
    this.saveSyncStatus();
    this.notifyStatusChange();
  }

  private notifyStatusChange() {
    this.statusCallbacks.forEach((callback) => {
      try {
        callback(this.syncStatus);
      } catch (error) {
        console.error("Error in sync status callback:", error);
      }
    });
  }

  private startPeriodicSync() {
    this.syncInterval = setInterval(() => {
      if (this.syncStatus.isOnline && !this.syncStatus.syncInProgress) {
        this.triggerSync({ backgroundSync: true });
      }
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  public onStatusChange(callback: (status: SyncStatus) => void) {
    this.statusCallbacks.add(callback);
    return () => this.statusCallbacks.delete(callback);
  }

  public getSyncStatus(): SyncStatus {
    return { ...this.syncStatus };
  }

  public async triggerSync(options: SyncOptions = {}): Promise<void> {
    if (!this.syncStatus.isOnline) {
      if (!options.backgroundSync) {
        notify.warning({
          title: "Offline",
          description:
            "Cannot sync while offline. Data will sync when connection is restored.",
        });
      }
      return;
    }

    if (this.syncStatus.syncInProgress) {
      if (!options.backgroundSync) {
        notify.info({
          title: "Sync in progress",
          description: "A sync operation is already running.",
        });
      }
      return;
    }

    this.updateSyncStatus({ syncInProgress: true });

    try {
      await this.performSync(options);
      this.updateSyncStatus({
        lastSyncTime: Date.now(),
        syncInProgress: false,
      });

      if (!options.backgroundSync) {
        notify.success({
          title: "Sync complete",
          description: "Your data has been synchronized successfully.",
        });
      }
    } catch (error) {
      this.updateSyncStatus({ syncInProgress: false });

      if (!options.backgroundSync) {
        notify.error({
          title: "Sync failed",
          description:
            error instanceof Error ? error.message : "Unknown sync error",
        });
      }

      console.error("Sync failed:", error);
      throw error;
    }
  }

  private async performSync(options: SyncOptions): Promise<void> {
    const totalSteps = 4;
    let currentStep = 0;

    const updateProgress = () => {
      currentStep++;
      const progress = (currentStep / totalSteps) * 100;
      options.onProgress?.(progress);
    };

    try {
      await this.syncDocuments();
      updateProgress();

      await this.syncFlashcards();
      updateProgress();

      await this.syncQuizzes();
      updateProgress();

      await this.cleanupOrphanedData();
      updateProgress();
    } catch (error) {
      console.error("Sync operation failed:", error);
      throw error;
    }
  }

  private async syncDocuments(): Promise<void> {
    try {
      const cloudDocuments = await getUserDocuments();
      const localDocuments =
        (await localforage.getItem<any[]>("uploaded-documents")) || [];

      const localDocumentIds = new Set(localDocuments.map((doc) => doc.id));
      const cloudDocumentIds = new Set(cloudDocuments.map((doc) => doc.id));

      const documentsToDownload = cloudDocuments.filter(
        (doc) => !localDocumentIds.has(doc.id)
      );
      const documentsToRemove = localDocuments.filter(
        (doc) => !cloudDocumentIds.has(doc.id)
      );

      if (documentsToDownload.length > 0) {
        const updatedLocalDocuments = [
          ...localDocuments,
          ...documentsToDownload,
        ];
        await localforage.setItem("uploaded-documents", updatedLocalDocuments);
      }

      if (documentsToRemove.length > 0) {
        const filteredLocalDocuments = localDocuments.filter((doc) =>
          cloudDocumentIds.has(doc.id)
        );
        await localforage.setItem("uploaded-documents", filteredLocalDocuments);
      }

      this.updateSyncStatus({
        pendingDownloads: 0,
      });
    } catch (error) {
      console.error("Document sync failed:", error);
      throw new Error("Failed to sync documents");
    }
  }

  private async syncFlashcards(): Promise<void> {
    try {
      const localDecks = await getFlashcardDecks();

      for (const deck of localDecks) {
        if (!deck.synced) {
          // Mark as synced after successful save to avoid duplicate uploads
          deck.synced = true;
          await localforage.setItem(`flashcard-deck-${deck.id}`, deck);
        }
      }

      this.updateSyncStatus({
        pendingUploads: 0,
      });
    } catch (error) {
      console.error("Flashcard sync failed:", error);
      throw new Error("Failed to sync flashcards");
    }
  }

  private async syncQuizzes(): Promise<void> {
    try {
      // Quiz sync implementation would go here
      // For now, just mark as complete
    } catch (error) {
      console.error("Quiz sync failed:", error);
      throw new Error("Failed to sync quizzes");
    }
  }

  private async cleanupOrphanedData(): Promise<void> {
    try {
      // Clean up any orphaned local data
      const allKeys = await localforage.keys();
      const documentKeys = allKeys.filter((key) =>
        key.startsWith("document-content-")
      );
      const validDocuments =
        (await localforage.getItem<any[]>("uploaded-documents")) || [];
      const validDocumentIds = new Set(validDocuments.map((doc) => doc.id));

      for (const key of documentKeys) {
        const documentId = key.replace("document-content-", "");
        if (!validDocumentIds.has(documentId)) {
          await localforage.removeItem(key);
        }
      }
    } catch (error) {
      console.error("Cleanup failed:", error);
    }
  }

  public async clearAllData(): Promise<void> {
    try {
      await localforage.clear();
      this.updateSyncStatus({
        lastSyncTime: null,
        pendingUploads: 0,
        pendingDownloads: 0,
      });

      notify.success({
        title: "Data cleared",
        description: "All local data has been cleared successfully.",
      });
    } catch (error) {
      notify.error({
        title: "Clear failed",
        description: "Failed to clear local data.",
      });
      throw error;
    }
  }

  public async exportAllData(): Promise<Blob> {
    try {
      const allData: Record<string, any> = {};
      const keys = await localforage.keys();

      for (const key of keys) {
        allData[key] = await localforage.getItem(key);
      }

      const dataString = JSON.stringify(allData, null, 2);
      return new Blob([dataString], { type: "application/json" });
    } catch (error) {
      console.error("Export failed:", error);
      throw new Error("Failed to export data");
    }
  }

  public async importData(jsonData: string): Promise<void> {
    try {
      const data = JSON.parse(jsonData);

      for (const [key, value] of Object.entries(data)) {
        await localforage.setItem(key, value);
      }

      await this.triggerSync({ forceSync: true });

      notify.success({
        title: "Data imported",
        description: "Your data has been imported and synced successfully.",
      });
    } catch (error) {
      notify.error({
        title: "Import failed",
        description: "Failed to import data. Please check the file format.",
      });
      throw error;
    }
  }

  public destroy() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }
    window.removeEventListener("online", this.triggerSync);
    window.removeEventListener("offline", this.triggerSync);
    document.removeEventListener("visibilitychange", this.triggerSync);
  }
}

export const dataSync = new DataSyncManager();

export const useSyncStatus = () => {
  const [status, setStatus] = React.useState<SyncStatus>(
    dataSync.getSyncStatus()
  );

  React.useEffect(() => {
    return dataSync.onStatusChange(setStatus);
  }, []);

  return status;
};

export const syncOperations = {
  triggerSync: (options?: SyncOptions) => dataSync.triggerSync(options),
  clearAllData: () => dataSync.clearAllData(),
  exportAllData: () => dataSync.exportAllData(),
  importData: (data: string) => dataSync.importData(data),
  getSyncStatus: () => dataSync.getSyncStatus(),
};
