# ChewyAI - Intelligent Study Assistant

<div align="center">

![ChewyAI Logo](generated-icon.png)

**Transform your study materials into interactive learning experiences with AI-powered content generation**

[![MIT License](https://img.shields.io/badge/License-MIT-green.svg)](https://choosealicense.com/licenses/mit/)
[![React](https://img.shields.io/badge/React-18.x-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.x-blue.svg)](https://www.typescriptlang.org/)
[![Supabase](https://img.shields.io/badge/Supabase-Database-green.svg)](https://supabase.com/)

</div>

## ✨ Features

### 📚 Document Management

- **Smart Upload**: Drag & drop support for PDF, DOCX, TXT, and Markdown files
- **Client-Side Processing**: Extract text locally for instant feedback
- **Secure Storage**: Private cloud storage with user-specific access
- **Progress Tracking**: Real-time upload progress with detailed error handling

### 🧠 AI-Powered Content Generation

- **Flashcard Generation**: Create study cards from any document
- **Quiz Creation**: Multiple choice, true/false, and short answer questions
- **OpenRouter Integration**: Support for multiple AI providers (Gemini, GPT, Claude)
- **Smart Content Analysis**: AI extracts key concepts and creates relevant questions

### 📖 Spaced Repetition System

- **Scientific Learning**: Evidence-based spaced repetition algorithm
- **Difficulty Tracking**: Rate flashcards as Easy, Medium, or Difficult
- **Optimal Scheduling**: Cards reappear based on your performance
- **Progress Analytics**: Track your learning progress over time

### 🎯 Interactive Quiz Experience

- **Beautiful UI**: Dark-themed, responsive quiz interface
- **Progress Tracking**: Real-time progress bar and timing
- **Detailed Results**: Score breakdown with performance grades
- **Retry Capability**: Take quizzes multiple times to improve

### ⌨️ Accessibility & Navigation

- **Keyboard Navigation**: Full keyboard support for all interactions
- **Screen Reader Support**: ARIA labels and semantic HTML
- **Mobile Responsive**: Works seamlessly on all devices
- **High Contrast**: Accessible color schemes and clear typography

### 🌙 Modern User Experience

- **Dark/Light Theme**: Toggle between themes with preference persistence
- **Smooth Animations**: Subtle transitions and micro-interactions
- **Loading States**: Skeleton loaders for better perceived performance
- **Error Handling**: Comprehensive error boundaries with recovery options

### 🔄 Data Synchronization

- **Offline-First**: Works without internet connection
- **Auto-Sync**: Automatic background synchronization when online
- **Data Export**: Export all data as JSON for backup
- **Cross-Device**: Access your study materials from anywhere

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm
- Supabase account and project
- OpenRouter API key (optional, for AI features)

### Installation

1. **Clone the repository**

   ```bash
   git clone https://github.com/yourusername/chewyai.git
   cd chewyai
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Environment Setup**

   ```bash
   cp env.example .env
   ```

   Edit `.env` with your configuration:

   ```env
   # Supabase Configuration
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

   # Server Configuration
   PORT=5000
   SUPABASE_URL=your_supabase_url
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

   # AI Provider (Optional)
   OPENROUTER_API_KEY=your_openrouter_key
   DEFAULT_AI_MODEL=google/gemini-pro
   ```

4. **Start Development**

   ```bash
   npm run dev
   ```

   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000

## 📖 User Guide

### Getting Started

1. **Create Account**: Sign up with email and password
2. **Upload Documents**: Drag and drop study materials (PDF, DOCX, etc.)
3. **Generate Content**: Use AI to create flashcards and quizzes
4. **Study**: Review flashcards with spaced repetition
5. **Test Knowledge**: Take quizzes to assess your understanding

### Keyboard Shortcuts

#### Flashcard Review

- `Space` - Flip card
- `←` / `→` - Navigate between cards
- `1` - Rate as Difficult
- `2` - Rate as Medium
- `3` - Rate as Easy

#### Quiz Taking

- `←` / `→` - Navigate questions
- `Ctrl/Cmd + Enter` - Submit quiz
- `Esc` - Exit quiz

#### General Navigation

- `Esc` - Close modals/go back
- `Tab` - Navigate interactive elements

### Study Workflow

1. **Document Upload**

   - Upload your study materials
   - Text is extracted and processed locally
   - Documents are securely stored in your private space

2. **Content Generation**

   - Select documents to generate from
   - Choose between flashcards or quizzes
   - AI analyzes content and creates relevant questions

3. **Spaced Repetition**

   - Review flashcards daily
   - Rate difficulty to optimize scheduling
   - Focus on challenging concepts

4. **Assessment**
   - Take quizzes to test knowledge
   - Review results and identify gaps
   - Generate new content for weak areas

## 🏗️ Architecture

### Frontend

- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **Radix UI** for accessible components
- **React Query** for state management
- **LocalForage** for offline storage

### Backend

- **Node.js** with Express and TypeScript
- **Supabase** for database and authentication
- **Zod** for request validation
- **Multer** for file handling

### Database

- **PostgreSQL** via Supabase
- **Row Level Security** for data protection
- **Real-time subscriptions** for live updates

### Security

- **JWT Authentication** for secure API access
- **Private Storage** with user-specific paths
- **Input Validation** on all endpoints
- **CORS Configuration** for safe browser requests

## 🔧 Development

### Project Structure

```
chewyai/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/    # Reusable UI components
│   │   ├── pages/         # Route components
│   │   ├── hooks/         # Custom React hooks
│   │   ├── lib/           # Utilities and configurations
│   │   └── types/         # TypeScript type definitions
├── server/                # Express backend
│   ├── routes/           # API endpoints
│   ├── middleware/       # Express middleware
│   └── db/               # Database utilities
├── shared/               # Shared TypeScript types
├── supabase/            # Database migrations and functions
└── tasks/               # Project planning and tasks
```

### Available Scripts

```bash
# Development
npm run dev          # Start both frontend and backend
npm run dev:client   # Start only frontend
npm run dev:server   # Start only backend

# Building
npm run build        # Build for production
npm run build:client # Build frontend only
npm run build:server # Build backend only

# Database
npm run db:migrate   # Run database migrations
npm run db:reset     # Reset database to initial state

# Testing
npm run test         # Run all tests
npm run test:client  # Run frontend tests
npm run test:server  # Run backend tests

# Linting
npm run lint         # Check code style
npm run lint:fix     # Fix linting issues
```

### Development Guidelines

1. **Code Style**: Follow the established TypeScript and ESLint rules
2. **Components**: Create reusable, accessible components with proper TypeScript
3. **Security**: Always validate inputs and use proper authentication
4. **Performance**: Optimize for loading speed and bundle size
5. **Accessibility**: Ensure keyboard navigation and screen reader support

## 🤝 Contributing

We welcome contributions! Please read our [Contributing Guidelines](CONTRIBUTING.md) for details on:

- Code of Conduct
- Development Process
- Pull Request Guidelines
- Issue Reporting

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Supabase](https://supabase.com/) for the amazing backend platform
- [OpenRouter](https://openrouter.ai/) for AI model access
- [Radix UI](https://www.radix-ui.com/) for accessible components
- [Tailwind CSS](https://tailwindcss.com/) for utility-first styling
- [Lucide](https://lucide.dev/) for beautiful icons

---

<div align="center">

**Built with ❤️ for learners everywhere**

[📚 Documentation](docs/) • [🐛 Report Bug](issues/) • [💡 Feature Request](issues/)

</div>
