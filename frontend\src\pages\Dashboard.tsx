import React, { useState } from "react";
import AppLayout from "@/components/layout/AppLayout";
import DashboardOverview from "@/components/dashboard/DashboardOverview";
import UploadSection from "@/components/dashboard/UploadSection";
import StudySection from "@/components/dashboard/StudySection";
import ExportSection from "@/components/export/ExportSection";
import { useQueryClient } from "@tanstack/react-query";
import { cacheDocument, saveFlashcards } from "@/lib/storage";
import { Document as DocumentType } from "@/types";
import { Flashcard } from "@/types";
import { Quiz } from "../../../shared/types/quiz";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { uploadDocumentSecure } from "@/lib/api";

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [currentDocument, setCurrentDocument] = useState<DocumentType | null>(
    null
  );
  const [generatedFlashcards, setGeneratedFlashcards] = useState<Flashcard[]>(
    []
  );

  const handleDocumentProcessed = async (document: DocumentType) => {
    setCurrentDocument(document);

    // Cache document locally for backward compatibility and offline access
    await cacheDocument(document);

    // Use secure backend upload instead of direct Supabase upload
    if (user) {
      try {
        console.log("Starting secure upload for document:", document.name);

        const uploadResult = await uploadDocumentSecure({
          fileName: document.name,
          content: document.content,
          contentType: document.type,
          sizeBytes: document.size,
          documentId: document.id, // Use the client-generated document ID for consistency
        });

        console.log("Secure upload successful:", uploadResult);

        toast({
          title: "Success",
          description: "Document processed and uploaded securely!",
        });

        // Invalidate queries to refresh the sidebar
        await queryClient.invalidateQueries({
          queryKey: ["study-documents", user.id],
        });
      } catch (error: any) {
        console.error("Secure upload failed:", error);
        toast({
          title: "Upload Failed",
          description:
            error.message ||
            "Failed to upload document securely. Document is cached locally only.",
          variant: "destructive",
        });
      }
    } else {
      toast({
        title: "Not Authenticated",
        description: "Please log in to save documents to the cloud.",
        variant: "destructive",
      });
    }
  };

  const handleFlashcardsGenerated = (flashcards: Flashcard[]) => {
    setGeneratedFlashcards(flashcards);
    if (currentDocument) {
      saveFlashcards(flashcards, currentDocument.id);
    }
  };

  const handleQuizGenerated = (quiz: Quiz) => {
    console.log("Quiz generated:", quiz);
    // You could add logic here to store the quiz or navigate to it
  };

  return (
    <AppLayout title="Dashboard">
      <div className="mb-6 bg-slate-800 p-6 rounded-lg text-purple-400">
        <h1 className="text-2xl font-medium text-purple-400 mb-1">Dashboard</h1>
        <p className="text-purple-300">
          Welcome to ChewyAI. Let's make studying more effective!
        </p>
      </div>

      <DashboardOverview />

      <div className="mb-8">
        <UploadSection
          onDocumentProcessed={handleDocumentProcessed}
          onFlashcardsGenerated={handleFlashcardsGenerated}
          onQuizGenerated={handleQuizGenerated}
        />
      </div>

      <div className="mb-8">
        <StudySection />
      </div>

      <div className="mb-8">
        <ExportSection />
      </div>
    </AppLayout>
  );
};

export default Dashboard;
