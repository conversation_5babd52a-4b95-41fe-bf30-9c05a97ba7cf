import dotenv from "dotenv";
dotenv.config();

// Direct configuration with updated values
export const supabaseConfig = {
  url: "https://hrdjfukhzbzksqaupqie.supabase.co",
  serviceRoleKey:
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhyZGpmdWtoemJ6a3NxYXVwcWllIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzI0Mjk0OSwiZXhwIjoyMDYyODE4OTQ5fQ.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0",
  dbPassword: process.env.VITE_DATABASE_PASSWORD || "",
};

// Validation - only check essential config
if (!supabaseConfig.url || !supabaseConfig.serviceRoleKey) {
  console.error(
    "❌ Error: Supabase URL and Service Role Key must be configured."
  );
  console.error("Current config:", {
    url: supabaseConfig.url ? "✓ Set" : "✗ Missing",
    serviceRoleKey: supabaseConfig.serviceRoleKey ? "✓ Set" : "✗ Missing",
  });
  process.exit(1);
}

// Log configuration status
console.log("✓ Supabase configuration loaded successfully");
console.log("✓ URL:", supabaseConfig.url ? "Configured" : "Missing");
console.log(
  "✓ Service Role Key:",
  supabaseConfig.serviceRoleKey ? "Configured" : "Missing"
);
console.log(
  "✓ Database Password:",
  supabaseConfig.dbPassword
    ? "Configured"
    : "Optional - using service role key authentication"
);
